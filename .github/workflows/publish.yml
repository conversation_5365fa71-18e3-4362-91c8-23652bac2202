name: Publish

# Run every Sunday @ 03:00 UTC
on:
  workflow_dispatch:
  schedule:
    - cron:  '0 3 * * 0'

jobs:
  remix:
    # Needed to make puppeteer work on GitHub Actions
    # See https://github.com/puppeteer/puppeteer/issues/12818
    # runs-on: ubuntu-latest
    runs-on: ubuntu-22.04
    steps:
    - name: Checkout workflow ⬇️
      uses: actions/checkout@ac593985615ec2ede58e132d2e21d2b1cbd6127c # v3
      with:
        repository: 'gauntface/workflows-static-site'
        path: 'workflows-static-site'

    - name: Checkout site ⬇️
      uses: actions/checkout@ac593985615ec2ede58e132d2e21d2b1cbd6127c # v3
      with:
        submodules: true
        path: 'react-site'

    - name: Install action NPM Deps 🌎
      run: |
        cd ./workflows-static-site/
        npm install

    - name: Install site NPM Deps 🌎
      run: |
        cd ./react-site/
        npm install

    - name: Configure AWS Credentials ☁️
      uses: aws-actions/configure-aws-credentials@v1-node16
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-west-1

    - name: Build 🔧
      run: |
        cd ./react-site/
        npm run build

    - name: Project Tests 🧪
      run: |
        cd ./react-site/
        npm run test --if-present

    - name: Deploy to AWS 🌤️
      run: npx --package @gauntface/cli gauntface aws s3 deploy-static-site --directory="./react-site/dist/" --bucket_name="brew.gaunt.dev" --delete_exclude="generated/*"
      shell: bash
