import { Button, Heading, RadioGroup } from "@radix-ui/themes";
import { useState } from "react";

export function Questions(props: QuestionsProps) {
  const [useLiquidYeast, setUseLiquidYeast] = useState<boolean | null>(null)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    props.onAnswers({
      useLiquidYeast: !!useLiquidYeast,
    })
  }

  return (
    <>
      <Heading>Brewing Preparation</Heading>
      <form onSubmit={handleSubmit}>

          <Heading size="3" weight={"medium"}>Are you using liquid yeast?</Heading>

          <RadioGroup.Root
            defaultValue={useLiquidYeast === null ? undefined : useLiquidYeast.toString()}
            onValueChange={(value) => setUseLiquidYeast(value === "liquid")}>
            <RadioGroup.Item value="liquid">Yes, I'm using liquid yeast</RadioGroup.Item>
            <RadioGroup.Item value="dry">No, I'm using dry yeast</RadioGroup.Item>
          </RadioGroup.Root>

        <Button type="submit" disabled={useLiquidYeast === null}>
          Show Checklist
        </Button>
      </form>
    </>
  );
}

type QuestionsProps = {
  onAnswers: (answers: QuestionAnswers) => void;
}

export interface QuestionAnswers {
  useLiquidYeast: boolean;
}
