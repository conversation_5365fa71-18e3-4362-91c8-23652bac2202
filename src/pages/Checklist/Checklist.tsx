import type { QuestionAnswers } from "../Questions/Questions";
import { ChecklistItem } from "../../components/ChecklistItem/ChecklistItem";
import { Button, Flex, Heading, Text, Box, Grid } from "@radix-ui/themes";
import { RocketIcon } from "@radix-ui/react-icons";
import { BrewSection } from "../../components/BrewSection/BrewSection";

import "./Checklist.css";

export function Checklist(props: ChecklistProps) {
  const handlePrint = () => {
    window.print()
  }

  const answers = props.answers;

  return (<>
    <Flex direction={"row"} align={"center"} justify={"between"} mb="4" className="u-print-hidden">
      <Button variant="outline" onClick={() => props.onBack()}>
        Back to Question
      </Button>
      <Button onClick={handlePrint} className="flex items-center gap-2">
        <RocketIcon />
        Print Checklist
      </Button>
    </Flex>

    <Heading size="8">Brewing Checklist</Heading>
    <Box className="u-print-hidden"><Text weight={"light"}>{answers.useLiquidYeast ? "Using Liquid Yeast" : "Using Dry Yeast"}</Text></Box>

    <Flex direction={"column"} mt="2">
        <Heading size="2" className="c-checklist__heading">Day Before</Heading>

        <Grid columns="auto 1fr" gap="2" my="2">
          <BrewSection title="Charge">
            <ChecklistItem>iSpindle</ChecklistItem>
            <ChecklistItem>Drill</ChecklistItem>
          </BrewSection>

          <BrewSection title="Clean">
            <ChecklistItem>Kettle</ChecklistItem>
            <ChecklistItem>Kettle lid</ChecklistItem>
            <ChecklistItem>False bottom</ChecklistItem>
            <ChecklistItem>Fermenter</ChecklistItem>
            <ChecklistItem>Fermenter lid</ChecklistItem>
            <ChecklistItem>Grain bag</ChecklistItem>
            <ChecklistItem>Thermometer</ChecklistItem>
            <ChecklistItem>Wort chiller</ChecklistItem>
            <ChecklistItem>Hop filter</ChecklistItem>
            <ChecklistItem>Paddle</ChecklistItem>
            <ChecklistItem>Aeration wand</ChecklistItem>
            <ChecklistItem>Gallon jug</ChecklistItem>
            <ChecklistItem>Cleaning bucket</ChecklistItem>
            <ChecklistItem>Sanitizing bucket</ChecklistItem>
            <ChecklistItem>Airlock</ChecklistItem>
            <ChecklistItem>Tubing for kettle to fermenter</ChecklistItem>
            <ChecklistItem>Scissors (for yeast)</ChecklistItem>
            <ChecklistItem>Baking Tray (for BIAB bag)</ChecklistItem>
            <ChecklistItem>Baking Rack (for BIAB bag)</ChecklistItem>
            {!answers.useLiquidYeast && (
              <ChecklistItem>Small measuring cup (for yeast)</ChecklistItem>
            )}
          </BrewSection>

          <BrewSection title="Prep">
            <ChecklistItem>Make ice for chilling wort</ChecklistItem>
            <ChecklistItem>Prepare your brewing water</ChecklistItem>
          </BrewSection>
        </Grid>

        <Heading size="2" className="c-checklist__heading">Brew Day</Heading>

        <Grid columns="auto 1fr" gap="2" my="2">
            <BrewSection title="Start">
              {answers.useLiquidYeast && (
                <ChecklistItem>Remove yeast from refrigerator</ChecklistItem>
              )}
            </BrewSection>

            <BrewSection title="Mash">
              <ChecklistItem>Check all ingredients are on hand</ChecklistItem>
              <ChecklistItem>Setup Propane Burner</ChecklistItem>
              <ChecklistItem>Check kettle valve is closed & fill kettle with 8.5 gallons of water</ChecklistItem>
              <ChecklistItem>Setup thermometer & heat strike water to correct temperature</ChecklistItem>
              <ChecklistItem>When mash water is ready, submerge grain bag and begin mash</ChecklistItem>
              <ChecklistItem>Place lid on kettle to help maintain temperature</ChecklistItem>
            </BrewSection>

            <BrewSection title="Clean & sanitize while mashing">
              <ChecklistItem>Fermenter</ChecklistItem>
              <ChecklistItem>Fermenter lid</ChecklistItem>
              <ChecklistItem>Wort chiller</ChecklistItem>
              <ChecklistItem>Aeration wand</ChecklistItem>
              <ChecklistItem>Scissors (for yeast)</ChecklistItem>
              <ChecklistItem>Airlock</ChecklistItem>
              <ChecklistItem>Tubing for kettle to fermenter</ChecklistItem>
              {!answers.useLiquidYeast && (
                <ChecklistItem>Small measuring cup (for yeast)</ChecklistItem>
              )}
            </BrewSection>

            <BrewSection title="Boil">
              <ChecklistItem>Remove grain bar from kettle</ChecklistItem>
              <ChecklistItem>Take a gravity reading of the mash <BlankHandwrittenSpace /></ChecklistItem>
              <ChecklistItem>Fire the kettle and bring to a boil</ChecklistItem>
              <ul>
                <ChecklistItem>On the way to boil, have brewers breakfast</ChecklistItem>
              </ul>
              <ChecklistItem>Sitr well as the hot break occurs</ChecklistItem>
              <ChecklistItem>Follow recipe</ChecklistItem>
              <ChecklistItem>Flame out</ChecklistItem>
              <ChecklistItem>Cool wort to fermentation temperature</ChecklistItem>
              <ChecklistItem>Take a gravity reading of the wort</ChecklistItem>
              <ChecklistItem>Position fermenter spigot to avoid trub during bottling</ChecklistItem>
              <ChecklistItem>Sanitize fermenter, air lock & aeration wand</ChecklistItem>
              <ChecklistItem>Transfer wort to fermenter</ChecklistItem>
              <ChecklistItem>Pitch yeast</ChecklistItem>
              <ChecklistItem>Seal fermenter with airlock</ChecklistItem>
            </BrewSection>
        </Grid>
    </Flex>
  </>);
}

type ChecklistProps = {
  answers: QuestionAnswers;
  onBack: () => void;
}
